const { execSync, spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('./ai/ai-service');

/**
 * 构建错误修复器
 * 继承自 AIService，具备 AI 修复能力
 */
class BuildFixer extends AIService {
  constructor(projectPath, options = {}) {
    super(options);

    this.projectPath = projectPath;
    this.options = {
      buildCommand: 'npm run build',
      maxRetries: 3,
      aiRepairer: null, // 兼容性保留，但优先使用内置 AI 服务
      ...options
    };

    this.buildStats = {
      buildAttempts: 0,
      buildSuccess: false,
      errorsFound: [],
      errorsFixed: 0,
      finalBuildSuccess: false
    };
  }

  /**
   * 执行构建并修复错误
   */
  async buildAndFix() {
    try {
      console.log(chalk.blue('🏗️  开始构建项目并修复错误...'));

      // 首次构建尝试
      let buildResult = await this.attemptBuild();
      this.buildStats.buildAttempts++;

      if (buildResult.success) {
        console.log(chalk.green('🎉 项目构建成功！'));
        this.buildStats.buildSuccess = true;
        this.buildStats.finalBuildSuccess = true;
        return { success: true, attempts: this.buildStats.buildAttempts };
      }

      // 分析构建错误
      let errors = this.parseErrors(buildResult.output);
      this.buildStats.errorsFound = errors;

      if (errors.length === 0) {
        console.log(chalk.yellow('⚠️  构建失败但无法解析错误信息'));
        return { success: false, reason: 'Unable to parse build errors' };
      }

      console.log(chalk.yellow(`发现 ${errors.length} 个构建错误，开始修复...`));

      // 尝试修复错误
      for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
        console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxRetries}...`));

        const fixResult = await this.fixErrors(errors);

        if (fixResult.fixed > 0) {
          console.log(chalk.green(`✅ 修复了 ${fixResult.fixed} 个错误`));
          this.buildStats.errorsFixed += fixResult.fixed;

          // 重新构建
          buildResult = await this.attemptBuild();
          this.buildStats.buildAttempts++;

          if (buildResult.success) {
            console.log(chalk.green('🎉 修复后构建成功！'));
            this.buildStats.finalBuildSuccess = true;
            break;
          } else {
            // 更新错误列表
            errors = this.parseErrors(buildResult.output);
            console.log(chalk.yellow(`仍有 ${errors.length} 个错误需要修复`));
          }
        } else {
          console.log(chalk.yellow('⚠️  本轮未能修复任何错误'));
        }
      }

      this.printBuildStats();

      return {
        success: this.buildStats.finalBuildSuccess,
        attempts: this.buildStats.buildAttempts,
        errorsFixed: this.buildStats.errorsFixed,
        remainingErrors: errors.length
      };

    } catch (error) {
      console.error(chalk.red('❌ 构建修复过程失败:'), error.message);
      throw error;
    }
  }

  /**
   * 尝试构建项目
   */
  async attemptBuild() {
    try {
      console.log(chalk.gray(`执行构建命令: ${this.options.buildCommand}`));

      const output = execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return {
        success: true,
        output: output
      };
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 解析构建错误
   */
  parseErrors(buildOutput) {
    const errors = [];

    // 确保 buildOutput 是字符串
    const outputStr = typeof buildOutput === 'string' ? buildOutput : String(buildOutput || '');
    const lines = outputStr.split('\n');

    let currentError = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检测错误开始
      const errorMatch = this.detectErrorStart(line);
      if (errorMatch) {
        if (currentError) {
          errors.push(currentError);
        }
        currentError = {
          type: errorMatch.type,
          file: errorMatch.file,
          line: errorMatch.line,
          column: errorMatch.column,
          message: errorMatch.message,
          fullMessage: [line],
          severity: errorMatch.severity || 'error'
        };
      } else if (currentError && this.isErrorContinuation(line)) {
        currentError.fullMessage.push(line);
        if (line.trim()) {
          currentError.message += ' ' + line.trim();
        }
      } else if (currentError) {
        errors.push(currentError);
        currentError = null;
      }
    }

    if (currentError) {
      errors.push(currentError);
    }

    return this.categorizeErrors(errors);
  }

  /**
   * 检测错误开始
   */
  detectErrorStart(line) {
    // TypeScript 错误
    const tsError = line.match(/(.+\.ts)\((\d+),(\d+)\):\s*(error|warning)\s*TS(\d+):\s*(.+)/);
    if (tsError) {
      return {
        type: 'typescript',
        file: tsError[1],
        line: parseInt(tsError[2]),
        column: parseInt(tsError[3]),
        severity: tsError[4],
        code: tsError[5],
        message: tsError[6]
      };
    }

    // Vue 编译错误
    const vueError = line.match(/(.+\.vue):(\d+):(\d+):\s*(.*)/);
    if (vueError) {
      return {
        type: 'vue',
        file: vueError[1],
        line: parseInt(vueError[2]),
        column: parseInt(vueError[3]),
        message: vueError[4]
      };
    }

    // Webpack/Vite 错误
    const webpackError = line.match(/ERROR in (.+)/);
    if (webpackError) {
      return {
        type: 'webpack',
        file: webpackError[1],
        message: line
      };
    }

    // ESLint 错误
    const eslintError = line.match(/(.+\.(?:js|vue|ts)):(\d+):(\d+):\s*(error|warning)\s*(.+)/);
    if (eslintError) {
      return {
        type: 'eslint',
        file: eslintError[1],
        line: parseInt(eslintError[2]),
        column: parseInt(eslintError[3]),
        severity: eslintError[4],
        message: eslintError[5]
      };
    }

    // 通用错误
    const genericError = line.match(/Error:\s*(.+)/);
    if (genericError) {
      return {
        type: 'generic',
        message: genericError[1]
      };
    }

    return null;
  }

  /**
   * 检查是否为错误信息的继续
   */
  isErrorContinuation(line) {
    return line.startsWith('    ') || line.startsWith('\t') ||
           line.includes('at ') || line.includes('in ');
  }

  /**
   * 分类错误
   */
  categorizeErrors(errors) {
    return errors.map(error => {
      // 根据错误信息分类
      if (error.message.includes('Cannot find module')) {
        error.category = 'missing-module';
      } else if (error.message.includes('Property') && error.message.includes('does not exist')) {
        error.category = 'property-not-exist';
      } else if (error.message.includes('is not a function')) {
        error.category = 'not-a-function';
      } else if (error.message.includes('Vue 2') || error.message.includes('Vue3')) {
        error.category = 'vue-version';
      } else if (error.message.includes('element-ui') || error.message.includes('element-plus')) {
        error.category = 'ui-library';
      } else {
        error.category = 'other';
      }

      return error;
    });
  }

  /**
   * 修复错误
   */
  async fixErrors(errors) {
    let fixedCount = 0;

    for (const error of errors) {
      try {
        const fixed = await this.fixSingleError(error);
        if (fixed) {
          fixedCount++;
        }
      } catch (fixError) {
        console.log(chalk.yellow(`⚠️  修复错误失败: ${error.file} - ${fixError.message}`));
      }
    }

    return { fixed: fixedCount };
  }

  /**
   * 修复单个错误
   */
  async fixSingleError(error) {
    console.log(chalk.gray(`🔧 修复: ${error.file || 'unknown'} - ${error.category}`));

    switch (error.category) {
      case 'missing-module':
        return await this.fixMissingModule(error);
      case 'property-not-exist':
        return await this.fixPropertyNotExist(error);
      case 'vue-version':
        return await this.fixVueVersionIssue(error);
      case 'ui-library':
        return await this.fixUILibraryIssue(error);
      default:
        return await this.fixWithAI(error);
    }
  }

  /**
   * 修复缺失模块错误
   */
  async fixMissingModule(error) {
    const moduleMatch = error.message.match(/Cannot find module ['"]([^'"]+)['"]/);
    if (!moduleMatch) return false;

    const moduleName = moduleMatch[1];

    // 常见的模块映射
    const moduleMapping = {
      'element-ui': 'element-plus',
      'vue-template-compiler': '@vue/compiler-sfc',
      '@vue/composition-api': null // Vue 3 内置
    };

    if (moduleMapping.hasOwnProperty(moduleName)) {
      const replacement = moduleMapping[moduleName];
      if (replacement) {
        return await this.replaceImport(error.file, moduleName, replacement);
      } else {
        return await this.removeImport(error.file, moduleName);
      }
    }

    return false;
  }

  /**
   * 修复属性不存在错误
   */
  async fixPropertyNotExist(error) {
    // 使用 AI 修复复杂的属性错误
    return await this.fixWithAI(error);
  }

  /**
   * 修复 Vue 版本问题
   */
  async fixVueVersionIssue(error) {
    return await this.fixWithAI(error);
  }

  /**
   * 修复 UI 库问题
   */
  async fixUILibraryIssue(error) {
    return await this.fixWithAI(error);
  }

  /**
   * 使用 AI 修复错误
   */
  async fixWithAI(error) {
    // 优先使用内置 AI 服务，如果不可用则使用外部 aiRepairer
    if (this.isEnabled()) {
      try {
        const result = await this.repairBuildError(error);
        return result.success;
      } catch (aiError) {
        console.log(chalk.yellow(`⚠️  内置 AI 修复失败: ${aiError.message}`));
      }
    }

    // 回退到外部 aiRepairer
    if (this.options.aiRepairer && this.options.aiRepairer.isEnabled()) {
      try {
        const result = await this.options.aiRepairer.repairSingleFile({
          file: error.file,
          absolutePath: path.join(this.projectPath, error.file),
          error: error.message
        }, this.projectPath);

        return result.success;
      } catch (aiError) {
        console.log(chalk.yellow(`⚠️  外部 AI 修复失败: ${aiError.message}`));
      }
    }

    return false;
  }

  /**
   * 使用 AI 修复构建错误
   */
  async repairBuildError(error) {
    const filePath = path.join(this.projectPath, error.file);

    try {
      // 读取原始文件内容
      const originalContent = await fs.readFile(filePath, 'utf8');

      // 生成构建错误专用提示
      const prompt = this.generateBuildErrorPrompt(originalContent, error);

      // 调用 AI 进行修复
      const repairedContent = await this.callAI(prompt);

      // 验证修复结果
      if (this.validateRepairedContent(repairedContent, originalContent)) {
        // 备份原文件
        await this.backupFile(filePath);

        // 写入修复后的内容
        await fs.writeFile(filePath, repairedContent, 'utf8');

        console.log(chalk.green(`✅ 构建错误修复成功: ${error.file}`));
        return {
          file: error.file,
          success: true,
          originalSize: originalContent.length,
          repairedSize: repairedContent.length
        };
      } else {
        console.log(chalk.yellow(`⚠️  构建错误修复结果验证失败: ${error.file}`));
        return {
          file: error.file,
          success: false,
          error: 'Validation failed'
        };
      }
    } catch (repairError) {
      console.log(chalk.red(`❌ 构建错误修复失败: ${error.file}`));
      return {
        file: error.file,
        success: false,
        error: repairError.message
      };
    }
  }

  /**
   * 生成构建错误专用提示词
   */
  generateBuildErrorPrompt(originalContent, error) {
    const fileExtension = path.extname(error.file);
    const errorMessage = error.message;
    const errorType = error.type;
    const errorCategory = error.category;

    let prompt = `你是一个专业的 Vue 2 到 Vue 3 迁移专家，现在需要修复构建过程中出现的错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**错误信息**：
- 错误类型：${errorType}
- 错误分类：${errorCategory}
- 错误消息：${errorMessage}
- 文件路径：${error.file}
${error.line ? `- 错误行号：${error.line}` : ''}
${error.column ? `- 错误列号：${error.column}` : ''}

**修复要求**：
1. **保持功能一致性**：修复后的代码必须保持原有功能不变
2. **Vue 3 兼容性**：确保代码符合 Vue 3 语法和最佳实践
3. **Element Plus 兼容性**：将 Element UI 组件正确替换为 Element Plus
4. **TypeScript 兼容性**：如果是 TypeScript 文件，确保类型定义正确
5. **构建兼容性**：修复后的代码必须能够通过构建流程

**常见构建错误修复指南**：`;

    // 根据错误类型添加特定指导
    switch (errorCategory) {
      case 'missing-module':
        prompt += `

**缺失模块错误修复**：
- 检查模块导入路径是否正确
- 将 Vue 2 相关模块替换为 Vue 3 对应模块
- 将 Element UI 导入替换为 Element Plus
- 移除 Vue 3 中已内置的模块（如 @vue/composition-api）`;
        break;

      case 'property-not-exist':
        prompt += `

**属性不存在错误修复**：
- 检查 Vue 3 中是否移除了该属性
- 使用 Vue 3 的新 API 替换已废弃的属性
- 更新组件实例的属性访问方式
- 修复 this.$refs 的访问方式`;
        break;

      case 'vue-version':
        prompt += `

**Vue 版本兼容性错误修复**：
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新生命周期钩子名称（如 beforeDestroy → beforeUnmount）
- 修复事件处理器的语法变化`;
        break;

      case 'ui-library':
        prompt += `

**UI 库兼容性错误修复**：
- 将 Element UI 组件名替换为 Element Plus 对应组件
- 更新组件属性名称和事件名称
- 修复图标引用方式
- 更新主题和样式引用`;
        break;

      default:
        prompt += `

**通用构建错误修复**：
- 检查语法错误和拼写错误
- 确保导入导出语句正确
- 修复类型定义问题
- 解决依赖冲突`;
    }

    // 根据文件类型添加特定指导
    if (fileExtension === '.vue') {
      prompt += `

**Vue 文件特定修复要求**：
- 更新 <template> 中的 Element UI 组件为 Element Plus
- 在 <script> 中使用 defineComponent 或 Composition API
- 更新事件处理和 refs 访问方式
- 确保 props 和 emits 正确定义
- 修复 v-model 的使用方式`;
    } else if (fileExtension === '.js' || fileExtension === '.ts') {
      prompt += `

**JavaScript/TypeScript 文件特定修复要求**：
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新 Vue 插件注册方式
- 修复导入语句和模块路径
- 更新类型定义（TypeScript）`;
    }

    prompt += `

**原始代码**：
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

**输出要求**：
1. 请仔细分析错误原因，提供修复后的完整代码
2. 使用 ${fileExtension.slice(1)} 代码块包装修复后的代码
3. 确保修复后的代码能够解决构建错误
4. 保持代码的可读性和维护性
5. 如果需要添加新的导入，请确保模块路径正确

请提供修复后的完整代码：`;

    return prompt;
  }

  /**
   * 替换导入语句
   */
  async replaceImport(filePath, oldModule, newModule) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;

      const content = await fs.readFile(fullPath, 'utf8');
      const newContent = content.replace(
        new RegExp(`(['"])${oldModule}\\1`, 'g'),
        `$1${newModule}$1`
      );

      if (newContent !== content) {
        await fs.writeFile(fullPath, newContent, 'utf8');
        console.log(chalk.green(`✅ 替换导入: ${oldModule} → ${newModule}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 替换导入失败: ${error.message}`));
    }

    return false;
  }

  /**
   * 移除导入语句
   */
  async removeImport(filePath, moduleName) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;

      const content = await fs.readFile(fullPath, 'utf8');
      const lines = content.split('\n');
      const newLines = lines.filter(line =>
        !line.includes(`'${moduleName}'`) && !line.includes(`"${moduleName}"`)
      );

      if (newLines.length !== lines.length) {
        await fs.writeFile(fullPath, newLines.join('\n'), 'utf8');
        console.log(chalk.green(`✅ 移除导入: ${moduleName}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 移除导入失败: ${error.message}`));
    }

    return false;
  }

  /**
   * 打印构建统计
   */
  printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.buildStats.buildAttempts} 次`);
    console.log(`发现错误: ${this.buildStats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.buildStats.errorsFixed} 个`);
    console.log(`最终状态: ${this.buildStats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);
  }

  /**
   * 验证修复后的内容（构建错误专用验证）
   */
  validateRepairedContent(repairedContent, originalContent) {
    // 调用基类的验证方法
    if (!super.validateRepairedContent(repairedContent, originalContent)) {
      return false;
    }

    // 构建错误特定的验证
    const buildErrorMarkers = ['COMPILATION_ERROR', 'BUILD_ERROR', 'SYNTAX_ERROR'];
    if (buildErrorMarkers.some(marker => repairedContent.includes(marker))) {
      return false;
    }

    // 检查是否包含基本的 Vue 组件结构
    if (originalContent.includes('<template>') && !repairedContent.includes('<template>')) {
      return false;
    }

    return true;
  }

  /**
   * 获取构建统计信息
   */
  getBuildStats() {
    return { ...this.buildStats };
  }
}

module.exports = BuildFixer;
