const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const CodeMigrator = require('./codeMigrator')

class ComponentMigrator extends CodeMigrator {
	constructor (inputPath, outputPath, options = {}) {
		super(inputPath, {
			srcDir: 'src/components',
			outputPath: outputPath,
			outputSrcDir: 'src/components',
			includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
			excludePatterns: ['node_modules/**', 'dist/**', 'build/**', '**/*.test.js', '**/*.spec.js'],
			copyMode: true,
			...options
		})

		this.componentStats = {
			totalComponents: 0,
			migratedComponents: 0,
			failedComponents: 0,
			skippedComponents: 0,
			componentTypes: {
				basic: 0,        // 基础组件
				form: 0,         // 表单组件
				table: 0,        // 表格组件
				chart: 0,        // 图表组件
				layout: 0,       // 布局组件
				business: 0      // 业务组件
			}
		}
	}

	/**
	 * 执行组件迁移
	 */
	async migrateComponents () {
		try {
			console.log(chalk.blue('🧩 开始迁移 Vue 组件...'))
			console.log(chalk.gray(`源路径: ${path.join(this.inputPath, this.options.srcDir)}`))
			console.log(chalk.gray(`目标路径: ${path.join(this.outputPath, this.options.outputSrcDir)}`))

			const sourceComponentsPath = path.join(this.inputPath, this.options.srcDir)
			if (!await fs.pathExists(sourceComponentsPath)) {
				console.log(chalk.yellow('⚠️  源项目中没有找到 components 目录，跳过组件迁移'))
				return { success: true, message: 'No components directory found' }
			}

			await this.ensureOutputDirectory()
			const result = await this.migrate()
			await this.analyzeComponentTypes()

			// 打印组件迁移统计
			this.printComponentStats()

			return {
				success: true,
				stats: this.stats,
				componentStats: this.componentStats
			}

		} catch (error) {
			console.error(chalk.red('❌ 组件迁移失败:'), error.message)
			throw error
		}
	}

	async analyzeComponentTypes () {
		try {
			const files = await this.getFilesToMigrate()
			this.componentStats.totalComponents = files.filter(file => file.endsWith('.vue')).length

			for (const filePath of files) {
				if (path.extname(filePath) === '.vue') {
					const componentType = await this.detectComponentType(filePath)
					this.componentStats.componentTypes[componentType]++
				}
			}
		} catch (error) {
			console.warn(chalk.yellow('⚠️  组件类型分析失败:'), error.message)
		}
	}

	async detectComponentType (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')
			const fileName = path.basename(filePath, '.vue').toLowerCase()

			// 基于文件名和内容检测组件类型
			if (fileName.includes('form') || content.includes('el-form') || content.includes('<form')) {
				return 'form'
			} else if (fileName.includes('table') || content.includes('el-table') || content.includes('<table')) {
				return 'table'
			} else if (fileName.includes('chart') || content.includes('echarts') || content.includes('chart')) {
				return 'chart'
			} else if (fileName.includes('layout') || fileName.includes('header') || fileName.includes('sidebar') || fileName.includes('footer')) {
				return 'layout'
			} else if (fileName.includes('business') || filePath.includes('/business/')) {
				return 'business'
			} else {
				return 'basic'
			}
		} catch (error) {
			return 'basic'
		}
	}

	/**
	 * 迁移 Vue 文件（重写以添加组件特定的转换）
	 */
	async migrateVueFile (source, filePath) {
		try {
			// 先执行基础的 Vue 文件迁移
			let transformedCode = await super.migrateVueFile(source, filePath)
			return transformedCode
		} catch (error) {
			throw new Error(`组件文件转换失败: ${error.message}`)
		}
	}

	printComponentStats () {
		console.log('\n' + chalk.bold('🧩 组件迁移统计:'))
		console.log(`总组件数: ${this.componentStats.totalComponents}`)

		console.log('\n组件类型分布:')
		Object.entries(this.componentStats.componentTypes).forEach(([type, count]) => {
			if (count > 0) {
				const typeNames = {
					basic: '基础组件',
					form: '表单组件',
					table: '表格组件',
					chart: '图表组件',
					layout: '布局组件',
					business: '业务组件'
				}
				console.log(`  ${typeNames[type]}: ${count} 个`)
			}
		})

		// 调用父类的统计打印
		this.printMigrationStats()
	}

	/**
	 * 生成组件迁移建议
	 */
	generateComponentRecommendations () {
		const recommendations = []

		if (this.componentStats.componentTypes.chart > 0) {
			recommendations.push('检查图表组件是否需要更新 ECharts 版本')
		}

		if (this.componentStats.componentTypes.form > 0) {
			recommendations.push('验证表单组件的验证规则是否正常工作')
		}

		if (this.componentStats.componentTypes.table > 0) {
			recommendations.push('测试表格组件的排序和筛选功能')
		}

		if (this.stats.failed > 0) {
			recommendations.push('手动检查迁移失败的组件')
		}

		recommendations.push('运行组件单元测试确保功能正常')
		recommendations.push('检查组件的样式是否需要调整')

		return recommendations
	}
}

module.exports = ComponentMigrator
