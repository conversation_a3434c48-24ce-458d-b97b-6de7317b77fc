const { execSync } = require('child_process')
const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const semver = require('semver')

/**
 * Vue 3 兼容性检查器
 */
class DependencyChecker {
	constructor (projectPath) {
		this.projectPath = projectPath
		this.packageJsonPath = path.join(projectPath, 'package.json')
		this.incompatibleDeps = []
		this.compatibleDeps = []
		this.unknownDeps = []
	}

	/**
	 * 检查所有依赖的 Vue 3 兼容性
	 */
	async checkCompatibility () {
		try {
			console.log(chalk.blue('🔍 开始检查依赖的 Vue 3 兼容性...'))

			const packageJson = await fs.readJson(this.packageJsonPath)
			const allDeps = {
				...packageJson.dependencies,
				...packageJson.devDependencies
			}

			const results = await this.checkDependencies(allDeps)
			this.printCompatibilityReport(results)

			return results
		} catch (error) {
			console.error(chalk.red('❌ 依赖兼容性检查失败:'), error.message)
			throw error
		}
	}

	/**
	 * 检查依赖列表（使用本地映射表，快速检查）
	 */
	async checkDependencies (dependencies) {
		const results = {
			compatible: [],
			incompatible: [],
			unknown: [],
			total: Object.keys(dependencies).length
		}

		console.log(chalk.gray(`正在检查 ${results.total} 个依赖...`))

		for (const [depName, version] of Object.entries(dependencies)) {
			const compatibility = this.checkSingleDependencyLocal(depName, version)
			results[compatibility.status].push(compatibility)

			// 显示进度
			process.stdout.write('.')
		}

		console.log('\n')
		return results
	}

	/**
	 * 检查单个依赖的兼容性（本地快速检查）
	 */
	checkSingleDependencyLocal (depName, version) {
		// 跳过一些明显的系统依赖
		if (this.isSystemDependency(depName)) {
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: 'System dependency'
			}
		}

		// 检查已知的不兼容包
		const knownIncompatible = this.getKnownIncompatiblePackages()
		if (knownIncompatible[depName]) {
			return {
				name: depName,
				version,
				status: 'incompatible',
				reason: 'Known incompatible with Vue 3',
				alternatives: knownIncompatible[depName].alternatives
			}
		}

		// 检查已知的兼容包
		const knownCompatible = this.getKnownCompatiblePackages()
		if (knownCompatible[depName]) {
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: 'Known compatible with Vue 3',
				recommendedVersion: knownCompatible[depName].version
			}
		}

		// 检查可能需要升级的包
		const needsUpgrade = this.getPackagesNeedingUpgrade()
		if (needsUpgrade[depName]) {
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: 'Compatible with upgrade',
				recommendedVersion: needsUpgrade[depName].version,
				note: needsUpgrade[depName].note
			}
		}

		// 默认认为是兼容的（非 Vue 相关包）
		return {
			name: depName,
			version,
			status: 'compatible',
			reason: 'Non-Vue specific package, likely compatible'
		}
	}

	/**
	 * 获取包信息
	 */
	async getPackageInfo (packageName) {
		try {
			const result = execSync(`npm view ${packageName} --json`, {
				encoding: 'utf8',
				timeout: 10000
			})

			const packageInfo = JSON.parse(result)

			return {
				name: packageName,
				latestVersion: packageInfo.version,
				peerDependencies: packageInfo.peerDependencies || {},
				keywords: packageInfo.keywords || [],
				description: packageInfo.description || ''
			}
		} catch (error) {
			throw new Error(`Failed to get package info for ${packageName}: ${error.message}`)
		}
	}

	/**
	 * 检查 Vue 3 支持
	 */
	checkVue3Support (packageInfo) {
		const { name, peerDependencies, keywords, latestVersion } = packageInfo

		// 检查已知的不兼容包
		const knownIncompatible = this.getKnownIncompatiblePackages()
		if (knownIncompatible[name]) {
			return {
				isCompatible: false,
				reason: 'Known incompatible with Vue 3',
				alternatives: knownIncompatible[name].alternatives
			}
		}

		// 检查已知的兼容包
		const knownCompatible = this.getKnownCompatiblePackages()
		if (knownCompatible[name]) {
			return {
				isCompatible: true,
				reason: 'Known compatible with Vue 3',
				recommendedVersion: knownCompatible[name].version || latestVersion
			}
		}

		// 检查 peerDependencies 中的 Vue 版本
		if (peerDependencies.vue) {
			const vueRange = peerDependencies.vue
			if (semver.intersects(vueRange, '>=3.0.0')) {
				return {
					isCompatible: true,
					reason: `Supports Vue 3 (peerDependencies: ${vueRange})`,
					recommendedVersion: latestVersion
				}
			} else if (semver.intersects(vueRange, '<3.0.0')) {
				return {
					isCompatible: false,
					reason: `Only supports Vue 2 (peerDependencies: ${vueRange})`
				}
			}
		}

		// 基于关键词判断
		const vueRelated = keywords.some(keyword =>
			keyword.includes('vue') || keyword.includes('element')
		)

		if (vueRelated) {
			return {
				isCompatible: false,
				reason: 'Vue-related package without Vue 3 support indication'
			}
		}

		// 默认认为是兼容的（非 Vue 相关包）
		return {
			isCompatible: true,
			reason: 'Non-Vue specific package, likely compatible',
			recommendedVersion: latestVersion
		}
	}

	/**
	 * 获取已知不兼容的包列表
	 */
	getKnownIncompatiblePackages () {
		return {
			'element-ui': {
				alternatives: ['element-plus']
			},
			'vue-template-compiler': {
				alternatives: ['@vue/compiler-sfc']
			},
			'@vue/composition-api': {
				alternatives: ['Built into Vue 3']
			},
			'vue-class-component': {
				alternatives: ['Use Composition API or Options API']
			},
			'vue-property-decorator': {
				alternatives: ['Use Composition API']
			},
			'vuex-class': {
				alternatives: ['Use Composition API with useStore']
			}
		}
	}

	/**
	 * 获取已知兼容的包列表
	 */
	getKnownCompatiblePackages () {
		return {
			'vue': { version: '^3.4.0' },
			'vue-router': { version: '^4.5.0' },
			'vuex': { version: '^4.1.0' },
			'element-plus': { version: '^2.9.0' },
			'@vue/compiler-sfc': { version: '^3.4.0' },
			'@vue/cli-service': { version: '^5.0.8' },
			'@vue/cli-plugin-babel': { version: '^5.0.8' },
			'@vue/cli-plugin-eslint': { version: '^5.0.8' },
			'@vue/test-utils': { version: '^2.4.6' },
			'eslint-plugin-vue': { version: '^10.2.0' },
			// 通用工具库
			'axios': {},
			'lodash': {},
			'moment': {},
			'dayjs': {},
			'echarts': {},
			'clipboard': {},
			'core-js': {},
			'js-cookie': {},
			'normalize.css': {},
			'nprogress': {},
			'screenfull': {},
			'file-saver': {},
			'xlsx': {},
			'jszip': {},
			'fuse.js': {},
			'sortablejs': {},
			'codemirror': {},
			'dropzone': {},
			'driver.js': {},
			'jsonlint': {},
			'path-to-regexp': {},
			'script-loader': {},
			'tui-editor': {}
		}
	}

	/**
	 * 获取需要升级的包列表
	 */
	getPackagesNeedingUpgrade () {
		return {
			'vuedraggable': {
				version: '^4.1.0',
				note: 'Vue 3 compatible version'
			},
			'vue-count-to': {
				version: '^1.0.13',
				note: 'Should work with Vue 3'
			},
			'vue-splitpane': {
				version: '^1.0.6',
				note: 'May need manual testing'
			}
		}
	}

	/**
	 * 判断是否为系统依赖
	 */
	isSystemDependency (depName) {
		const systemDeps = [
			// Node.js 相关
			'fs-extra', 'path', 'util', 'crypto', 'os', 'http', 'https',
			// CLI 工具
			'chalk', 'commander', 'inquirer', 'ora', 'semver', 'plop',
			// 构建工具
			'webpack', 'babel', 'eslint', 'prettier', 'jest', 'autoprefixer',
			// Vue CLI 相关
			'@vue/cli', 'vue-cli-service',
			// 开发工具
			'husky', 'lint-staged', 'mockjs', 'chokidar', 'connect',
			'html-webpack-plugin', 'script-ext-html-webpack-plugin',
			'sass', 'sass-loader', 'svg-sprite-loader', 'svgo',
			'serve-static', 'runjs'
		]

		return systemDeps.some(sysDep =>
			depName === sysDep || depName.startsWith(sysDep + '-') || depName.includes(sysDep)
		)
	}

	/**
	 * 打印兼容性报告
	 */
	printCompatibilityReport (results) {
		console.log('\n' + chalk.bold('📊 Vue 3 兼容性检查报告:'))
		console.log(`总计: ${results.total} 个依赖\n`)

		// 兼容的依赖
		if (results.compatible.length > 0) {
			console.log(chalk.green(`✅ 兼容 Vue 3 (${results.compatible.length}个):`))
			results.compatible.forEach(dep => {
				console.log(`  ${dep.name} - ${dep.reason}`)
			})
			console.log('')
		}

		// 不兼容的依赖
		if (results.incompatible.length > 0) {
			console.log(chalk.red(`❌ 不兼容 Vue 3 (${results.incompatible.length}个):`))
			results.incompatible.forEach(dep => {
				console.log(`  ${dep.name} - ${dep.reason}`)
				if (dep.alternatives) {
					console.log(`    建议替换为: ${dep.alternatives.join(', ')}`)
				}
			})
			console.log('')
		}

		// 未知状态的依赖
		if (results.unknown.length > 0) {
			console.log(chalk.yellow(`❓ 无法确定 (${results.unknown.length}个):`))
			results.unknown.forEach(dep => {
				console.log(`  ${dep.name} - 需要手动检查`)
			})
			console.log('')
		}

		// 总结
		const compatibilityRate = ((results.compatible.length / results.total) * 100).toFixed(1)
		console.log(chalk.bold(`兼容性: ${compatibilityRate}%`))

		if (results.incompatible.length > 0) {
			console.log(chalk.yellow('\n⚠️  建议在继续迁移前先解决不兼容的依赖问题。'))
		}
	}
}

module.exports = DependencyChecker
