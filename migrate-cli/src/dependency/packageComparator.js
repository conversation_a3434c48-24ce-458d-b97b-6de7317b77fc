const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * Package.json 对比器
 * 用于对比新旧工程的 package.json，识别需要迁移的依赖差异
 */
class PackageComparator {
  constructor(oldProjectPath, newProjectPath) {
    this.oldProjectPath = path.resolve(oldProjectPath);
    this.newProjectPath = path.resolve(newProjectPath);
    this.oldPackageJsonPath = path.join(this.oldProjectPath, 'package.json');
    this.newPackageJsonPath = path.join(this.newProjectPath, 'package.json');
  }

  /**
   * 对比两个项目的 package.json
   */
  async compare() {
    try {
      console.log(chalk.blue('📦 开始对比新旧工程的 package.json...'));

      // 检查文件是否存在
      await this.validatePackageFiles();

      // 读取 package.json 文件
      const oldPackage = await fs.readJson(this.oldPackageJsonPath);
      const newPackage = await fs.readJson(this.newPackageJsonPath);

      // 执行对比分析
      const comparison = this.analyzePackages(oldPackage, newPackage);

      // 打印对比结果
      this.printComparisonResult(comparison);

      console.log(chalk.green('✅ package.json 对比完成!'));
      return comparison;
    } catch (error) {
      console.error(chalk.red('❌ package.json 对比失败:'), error.message);
      throw error;
    }
  }

  /**
   * 验证 package.json 文件是否存在
   */
  async validatePackageFiles() {
    if (!await fs.pathExists(this.oldPackageJsonPath)) {
      throw new Error(`旧项目的 package.json 不存在: ${this.oldPackageJsonPath}`);
    }

    if (!await fs.pathExists(this.newPackageJsonPath)) {
      throw new Error(`新项目的 package.json 不存在: ${this.newPackageJsonPath}`);
    }
  }

  /**
   * 分析两个 package.json 的差异
   */
  analyzePackages(oldPackage, newPackage) {
    const result = {
      oldProject: {
        name: oldPackage.name,
        version: oldPackage.version,
        vueVersion: this.getVueVersion(oldPackage)
      },
      newProject: {
        name: newPackage.name,
        version: newPackage.version,
        vueVersion: this.getVueVersion(newPackage)
      },
      dependencies: {
        toAdd: [],      // 新项目有但旧项目没有的依赖
        toRemove: [],   // 旧项目有但新项目没有的依赖
        toUpdate: [],   // 版本不同的依赖
        unchanged: []   // 相同的依赖
      },
      devDependencies: {
        toAdd: [],
        toRemove: [],
        toUpdate: [],
        unchanged: []
      },
      scripts: {
        toAdd: [],
        toRemove: [],
        toUpdate: [],
        unchanged: []
      },
      migrationNeeded: false
    };

    // 分析 dependencies
    this.compareDependencies(
      oldPackage.dependencies || {},
      newPackage.dependencies || {},
      result.dependencies
    );

    // 分析 devDependencies
    this.compareDependencies(
      oldPackage.devDependencies || {},
      newPackage.devDependencies || {},
      result.devDependencies
    );

    // 分析 scripts
    this.compareScripts(
      oldPackage.scripts || {},
      newPackage.scripts || {},
      result.scripts
    );

    // 判断是否需要迁移
    result.migrationNeeded = this.needsMigration(result);

    return result;
  }

  /**
   * 对比依赖
   */
  compareDependencies(oldDeps, newDeps, result) {
    const allDeps = new Set([...Object.keys(oldDeps), ...Object.keys(newDeps)]);

    for (const depName of allDeps) {
      const oldVersion = oldDeps[depName];
      const newVersion = newDeps[depName];

      if (!oldVersion && newVersion) {
        // 新项目有，旧项目没有
        result.toAdd.push({ name: depName, version: newVersion });
      } else if (oldVersion && !newVersion) {
        // 旧项目有，新项目没有
        result.toRemove.push({ name: depName, version: oldVersion });
      } else if (oldVersion && newVersion) {
        if (oldVersion !== newVersion) {
          // 版本不同
          result.toUpdate.push({
            name: depName,
            oldVersion,
            newVersion
          });
        } else {
          // 版本相同
          result.unchanged.push({ name: depName, version: oldVersion });
        }
      }
    }
  }

  /**
   * 对比脚本
   */
  compareScripts(oldScripts, newScripts, result) {
    const allScripts = new Set([...Object.keys(oldScripts), ...Object.keys(newScripts)]);

    for (const scriptName of allScripts) {
      const oldScript = oldScripts[scriptName];
      const newScript = newScripts[scriptName];

      if (!oldScript && newScript) {
        result.toAdd.push({ name: scriptName, script: newScript });
      } else if (oldScript && !newScript) {
        result.toRemove.push({ name: scriptName, script: oldScript });
      } else if (oldScript && newScript) {
        if (oldScript !== newScript) {
          result.toUpdate.push({
            name: scriptName,
            oldScript,
            newScript
          });
        } else {
          result.unchanged.push({ name: scriptName, script: oldScript });
        }
      }
    }
  }

  /**
   * 获取 Vue 版本
   */
  getVueVersion(packageJson) {
    return packageJson.dependencies?.vue || 
           packageJson.devDependencies?.vue || 
           'unknown';
  }

  /**
   * 判断是否需要迁移
   */
  needsMigration(result) {
    const hasChanges = 
      result.dependencies.toAdd.length > 0 ||
      result.dependencies.toRemove.length > 0 ||
      result.dependencies.toUpdate.length > 0 ||
      result.devDependencies.toAdd.length > 0 ||
      result.devDependencies.toRemove.length > 0 ||
      result.devDependencies.toUpdate.length > 0;

    const vueVersionDifferent = 
      result.oldProject.vueVersion !== result.newProject.vueVersion;

    return hasChanges || vueVersionDifferent;
  }

  /**
   * 打印对比结果
   */
  printComparisonResult(result) {
    console.log('\n' + chalk.bold('📊 Package.json 对比结果:'));
    
    // 项目信息
    console.log(chalk.blue('\n🏷️  项目信息:'));
    console.log(`  旧项目: ${result.oldProject.name} (Vue ${result.oldProject.vueVersion})`);
    console.log(`  新项目: ${result.newProject.name} (Vue ${result.newProject.vueVersion})`);

    // Dependencies 变化
    if (this.hasChanges(result.dependencies)) {
      console.log(chalk.blue('\n📦 Dependencies 变化:'));
      this.printDependencyChanges(result.dependencies);
    }

    // DevDependencies 变化
    if (this.hasChanges(result.devDependencies)) {
      console.log(chalk.blue('\n🔧 DevDependencies 变化:'));
      this.printDependencyChanges(result.devDependencies);
    }

    // Scripts 变化
    if (this.hasChanges(result.scripts)) {
      console.log(chalk.blue('\n📜 Scripts 变化:'));
      this.printScriptChanges(result.scripts);
    }

    // 迁移建议
    if (result.migrationNeeded) {
      console.log(chalk.yellow('\n💡 迁移建议:'));
      console.log('  建议执行依赖迁移以同步新旧项目的配置');
    } else {
      console.log(chalk.green('\n✅ 两个项目的配置基本一致，无需特殊迁移'));
    }
  }

  /**
   * 检查是否有变化
   */
  hasChanges(changes) {
    return changes.toAdd.length > 0 || 
           changes.toRemove.length > 0 || 
           changes.toUpdate.length > 0;
  }

  /**
   * 打印依赖变化
   */
  printDependencyChanges(changes) {
    if (changes.toAdd.length > 0) {
      console.log(chalk.green(`  ➕ 需要添加 (${changes.toAdd.length}):`));
      changes.toAdd.forEach(dep => {
        console.log(chalk.gray(`    ${dep.name}@${dep.version}`));
      });
    }

    if (changes.toRemove.length > 0) {
      console.log(chalk.red(`  ➖ 需要移除 (${changes.toRemove.length}):`));
      changes.toRemove.forEach(dep => {
        console.log(chalk.gray(`    ${dep.name}@${dep.version}`));
      });
    }

    if (changes.toUpdate.length > 0) {
      console.log(chalk.yellow(`  🔄 需要更新 (${changes.toUpdate.length}):`));
      changes.toUpdate.forEach(dep => {
        console.log(chalk.gray(`    ${dep.name}: ${dep.oldVersion} → ${dep.newVersion}`));
      });
    }
  }

  /**
   * 打印脚本变化
   */
  printScriptChanges(changes) {
    if (changes.toAdd.length > 0) {
      console.log(chalk.green(`  ➕ 需要添加 (${changes.toAdd.length}):`));
      changes.toAdd.forEach(script => {
        console.log(chalk.gray(`    ${script.name}: ${script.script}`));
      });
    }

    if (changes.toRemove.length > 0) {
      console.log(chalk.red(`  ➖ 需要移除 (${changes.toRemove.length}):`));
      changes.toRemove.forEach(script => {
        console.log(chalk.gray(`    ${script.name}: ${script.script}`));
      });
    }

    if (changes.toUpdate.length > 0) {
      console.log(chalk.yellow(`  🔄 需要更新 (${changes.toUpdate.length}):`));
      changes.toUpdate.forEach(script => {
        console.log(chalk.gray(`    ${script.name}:`));
        console.log(chalk.gray(`      旧: ${script.oldScript}`));
        console.log(chalk.gray(`      新: ${script.newScript}`));
      });
    }
  }

  /**
   * 生成迁移建议
   */
  generateMigrationSuggestions(result) {
    const suggestions = [];

    if (result.dependencies.toAdd.length > 0) {
      suggestions.push(`添加 ${result.dependencies.toAdd.length} 个新依赖`);
    }

    if (result.dependencies.toRemove.length > 0) {
      suggestions.push(`移除 ${result.dependencies.toRemove.length} 个旧依赖`);
    }

    if (result.dependencies.toUpdate.length > 0) {
      suggestions.push(`更新 ${result.dependencies.toUpdate.length} 个依赖版本`);
    }

    return suggestions;
  }
}

module.exports = PackageComparator;
