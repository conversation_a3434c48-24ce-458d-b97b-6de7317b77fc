const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * Vue 2 到 Vue 3 依赖映射表
 */
const DEPENDENCY_MAPPING = {
  // Vue 核心依赖
  'vue': '^3.4.0',
  'vue-router': '^4.5.0',
  'vuex': '^4.1.0',
  
  // Element UI 到 Element Plus
  'element-ui': null, // 删除
  'element-plus': '^2.9.0',
  
  // Vue 编译器
  'vue-template-compiler': null, // 删除
  '@vue/compiler-sfc': '^3.4.0',
  
  // Vue CLI 相关
  '@vue/cli-service': '^5.0.8',
  '@vue/cli-plugin-babel': '^5.0.8',
  '@vue/cli-plugin-eslint': '^5.0.8',
  '@vue/cli-plugin-unit-jest': '^5.0.8',
  
  // ESLint 相关
  'eslint-plugin-vue': '^10.2.0',
  '@vue/eslint-config-standard': '^9.0.1',
  
  // 测试相关
  '@vue/test-utils': '^2.4.6',
  
  // 其他常用依赖
  'vue-count-to': '^1.0.13', // 保持不变
  'vue-splitpane': '^1.0.6',
  'vuedraggable': '^4.1.0', // Vue 3 兼容版本
};

/**
 * 需要删除的依赖
 */
const DEPENDENCIES_TO_REMOVE = [
  'vue-template-compiler',
  'element-ui',
  '@vue/composition-api' // Vue 3 内置
];

/**
 * 需要添加的新依赖
 */
const NEW_DEPENDENCIES = {
  'element-plus': '^2.9.0',
  '@element-plus/icons-vue': '^2.3.1',
  '@vue/compiler-sfc': '^3.4.0'
};

class PackageUpgrader {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.packageJsonPath = path.join(projectPath, 'package.json');
    this.backupPath = path.join(projectPath, 'package.json.backup');
  }

  /**
   * 升级 package.json 中的 Vue 相关依赖
   */
  async upgrade() {
    try {
      console.log(chalk.blue('📦 开始升级 package.json 依赖...'));
      
      // 检查 package.json 是否存在
      if (!await fs.pathExists(this.packageJsonPath)) {
        throw new Error(`package.json 不存在: ${this.packageJsonPath}`);
      }

      // 备份原始文件
      await this.createBackup();
      
      // 读取 package.json
      const packageJson = await fs.readJson(this.packageJsonPath);
      
      // 升级依赖
      const result = this.upgradeDependencies(packageJson);
      
      // 写入更新后的 package.json
      await fs.writeJson(this.packageJsonPath, packageJson, { spaces: 2 });
      
      console.log(chalk.green('✅ package.json 依赖升级完成!'));
      this.printUpgradeResult(result);
      
      return result;
    } catch (error) {
      console.error(chalk.red('❌ package.json 依赖升级失败:'), error.message);
      throw error;
    }
  }

  /**
   * 创建备份文件
   */
  async createBackup() {
    await fs.copy(this.packageJsonPath, this.backupPath);
    console.log(chalk.yellow(`📋 已创建备份: ${this.backupPath}`));
  }

  /**
   * 升级依赖版本
   */
  upgradeDependencies(packageJson) {
    const result = {
      upgraded: [],
      added: [],
      removed: [],
      unchanged: []
    };

    // 处理 dependencies
    if (packageJson.dependencies) {
      this.processDependencies(packageJson.dependencies, result, 'dependencies');
    }

    // 处理 devDependencies
    if (packageJson.devDependencies) {
      this.processDependencies(packageJson.devDependencies, result, 'devDependencies');
    }

    // 添加新依赖
    this.addNewDependencies(packageJson, result);

    // 更新脚本
    this.updateScripts(packageJson);

    return result;
  }

  /**
   * 处理依赖对象
   */
  processDependencies(deps, result, type) {
    Object.keys(deps).forEach(depName => {
      if (DEPENDENCIES_TO_REMOVE.includes(depName)) {
        // 删除依赖
        delete deps[depName];
        result.removed.push({ name: depName, type });
      } else if (DEPENDENCY_MAPPING[depName]) {
        // 升级依赖
        const oldVersion = deps[depName];
        const newVersion = DEPENDENCY_MAPPING[depName];
        deps[depName] = newVersion;
        result.upgraded.push({ 
          name: depName, 
          oldVersion, 
          newVersion, 
          type 
        });
      } else {
        // 保持不变
        result.unchanged.push({ name: depName, version: deps[depName], type });
      }
    });
  }

  /**
   * 添加新依赖
   */
  addNewDependencies(packageJson, result) {
    if (!packageJson.dependencies) {
      packageJson.dependencies = {};
    }

    Object.entries(NEW_DEPENDENCIES).forEach(([depName, version]) => {
      if (!packageJson.dependencies[depName] && !packageJson.devDependencies?.[depName]) {
        packageJson.dependencies[depName] = version;
        result.added.push({ name: depName, version, type: 'dependencies' });
      }
    });
  }

  /**
   * 更新脚本命令
   */
  updateScripts(packageJson) {
    if (!packageJson.scripts) return;

    // 更新构建脚本以使用 Vite（如果需要）
    if (packageJson.scripts.dev && packageJson.scripts.dev.includes('vue-cli-service')) {
      // 保持 vue-cli-service，但可以在后续步骤中提示用户考虑迁移到 Vite
    }
  }

  /**
   * 打印升级结果
   */
  printUpgradeResult(result) {
    console.log('\n' + chalk.bold('📊 升级结果统计:'));
    
    if (result.upgraded.length > 0) {
      console.log(chalk.green(`\n✅ 已升级 (${result.upgraded.length}个):`));
      result.upgraded.forEach(dep => {
        console.log(`  ${dep.name}: ${dep.oldVersion} → ${dep.newVersion}`);
      });
    }

    if (result.added.length > 0) {
      console.log(chalk.blue(`\n➕ 已添加 (${result.added.length}个):`));
      result.added.forEach(dep => {
        console.log(`  ${dep.name}: ${dep.version}`);
      });
    }

    if (result.removed.length > 0) {
      console.log(chalk.red(`\n🗑️  已删除 (${result.removed.length}个):`));
      result.removed.forEach(dep => {
        console.log(`  ${dep.name}`);
      });
    }

    if (result.unchanged.length > 0) {
      console.log(chalk.gray(`\n⏸️  保持不变 (${result.unchanged.length}个)`));
    }
  }

  /**
   * 恢复备份
   */
  async restoreBackup() {
    if (await fs.pathExists(this.backupPath)) {
      await fs.copy(this.backupPath, this.packageJsonPath);
      console.log(chalk.yellow('🔄 已恢复 package.json 备份'));
    }
  }
}

module.exports = PackageUpgrader;
