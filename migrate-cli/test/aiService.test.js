const assert = require('assert');
const fs = require('fs-extra');
const path = require('path');
const { AIService } = require('../src/ai/ai-service');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

function testAIService() {
  console.log('🧪 Testing AIService...');

  // Test constructor
  const service = new AIService();
  assert.strictEqual(service.options.maxTokens, 4000);
  assert.strictEqual(service.options.temperature, 0.1);
  assert.strictEqual(service.options.maxRetries, 3);
  console.log('  ✅ Constructor works correctly');

  // Test isEnabled
  assert.strictEqual(typeof service.isEnabled(), 'boolean');
  console.log('  ✅ isEnabled() method works');

  // Test validateRepairedContent
  const valid = service.validateRepairedContent(
    'export default { name: "Test" }',
    'export default { name: "Original" }'
  );
  assert.strictEqual(valid, true);
  console.log('  ✅ validateRepairedContent() works');

  // Test stats
  const stats = service.getStats();
  assert.strictEqual(typeof stats, 'object');
  assert.strictEqual(stats.attempted, 0);
  console.log('  ✅ getStats() works');

  console.log('✅ AIService tests passed');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testAIService();
}

module.exports = { testAIService };
