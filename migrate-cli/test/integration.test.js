const assert = require('assert');
const BuildFixer = require('../src/buildFixer');
const AIRepairer = require('../src/aiRepairer');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

function testIntegration() {
  console.log('🧪 Testing Integration...');

  // Test BuildFixer and AIRepairer integration
  const aiRepairer = new AIRepairer();
  const buildFixer = new BuildFixer('/test/path', {
    buildCommand: 'echo "test"',
    aiRepairer: aiRepairer
  });

  assert.strictEqual(buildFixer.options.aiRepairer, aiRepairer);
  console.log('  ✅ BuildFixer integrates with AIRepairer');

  // Test inheritance
  const { AIService } = require('../src/ai/ai-service');
  assert(buildFixer instanceof AIService);
  console.log('  ✅ BuildFixer inherits from AIService');

  console.log('✅ Integration tests passed');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testIntegration();
}

module.exports = { testIntegration };
