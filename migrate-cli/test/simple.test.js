#!/usr/bin/env node

/**
 * 简化的测试文件，用于快速验证核心功能
 */

const assert = require('assert');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

// 设置测试环境变量
process.env.DEEPSEEK_TOKEN = 'test-token';

const { AIService } = require('../src/ai/ai-service');
const BuildFixer = require('../src/buildFixer');
const AIRepairer = require('../src/aiRepairer');

async function runTests() {
  console.log(chalk.blue('🧪 运行简化测试套件...\n'));

  let passed = 0;
  let failed = 0;

  // 测试 1: AIService 基本功能
  try {
    console.log(chalk.yellow('📋 测试 AIService 基本功能'));

    const aiService = new AIService({
      maxTokens: 1000,
      temperature: 0.1
    });

    // 测试配置
    assert.strictEqual(aiService.options.maxTokens, 1000);
    assert.strictEqual(aiService.options.temperature, 0.1);
    console.log(chalk.green('  ✅ 配置正确'));

    // 测试状态
    assert.strictEqual(typeof aiService.isEnabled(), 'boolean');
    console.log(chalk.green('  ✅ isEnabled() 方法正常'));

    // 测试统计
    const stats = aiService.getStats();
    assert.strictEqual(typeof stats, 'object');
    assert.strictEqual(stats.attempted, 0);
    console.log(chalk.green('  ✅ getStats() 方法正常'));

    // 测试内容验证
    const validContent = aiService.validateRepairedContent(
      'export default { name: "Test" }',
      'export default { name: "Original" }'
    );
    assert.strictEqual(validContent, true);
    console.log(chalk.green('  ✅ validateRepairedContent() 方法正常'));

    passed++;
  } catch (error) {
    console.log(chalk.red('  ❌ AIService 测试失败:'), error.message);
    failed++;
  }

  // 测试 2: BuildFixer 基本功能
  try {
    console.log(chalk.yellow('\n📋 测试 BuildFixer 基本功能'));

    const tempDir = path.join(__dirname, 'temp-simple');
    await fs.ensureDir(tempDir);

    const buildFixer = new BuildFixer(tempDir, {
      buildCommand: 'echo "test build"',
      maxRetries: 2
    });

    // 测试配置
    assert.strictEqual(buildFixer.projectPath, tempDir);
    assert.strictEqual(buildFixer.options.maxRetries, 2);
    console.log(chalk.green('  ✅ 配置正确'));

    // 测试错误检测
    const tsError = buildFixer.detectErrorStart('src/test.ts(10,5): error TS2304: Cannot find name "Vue".');
    assert.strictEqual(tsError.type, 'typescript');
    assert.strictEqual(tsError.file, 'src/test.ts');
    console.log(chalk.green('  ✅ detectErrorStart() 方法正常'));

    // 测试错误分类
    const errors = [{
      message: 'Cannot find module "element-ui"',
      type: 'webpack'
    }];
    const categorized = buildFixer.categorizeErrors(errors);
    assert.strictEqual(categorized[0].category, 'missing-module');
    console.log(chalk.green('  ✅ categorizeErrors() 方法正常'));

    // 测试提示词生成
    const error = {
      type: 'vue',
      category: 'ui-library',
      message: 'el-button not found',
      file: 'test.vue'
    };
    const prompt = buildFixer.generateBuildErrorPrompt('test content', error);
    assert(prompt.includes('构建错误修复'));
    console.log(chalk.green('  ✅ generateBuildErrorPrompt() 方法正常'));

    // 测试统计
    const buildStats = buildFixer.getBuildStats();
    assert.strictEqual(typeof buildStats, 'object');
    assert.strictEqual(buildStats.buildAttempts, 0);
    console.log(chalk.green('  ✅ getBuildStats() 方法正常'));

    // 清理
    await fs.remove(tempDir);

    passed++;
  } catch (error) {
    console.log(chalk.red('  ❌ BuildFixer 测试失败:'), error.message);
    failed++;
  }

  // 测试 3: AIRepairer 基本功能
  try {
    console.log(chalk.yellow('\n📋 测试 AIRepairer 基本功能'));

    const aiRepairer = new AIRepairer({
      maxTokens: 2000,
      temperature: 0.2
    });

    // 测试配置
    assert.strictEqual(aiRepairer.options.maxTokens, 2000);
    assert.strictEqual(aiRepairer.options.temperature, 0.2);
    console.log(chalk.green('  ✅ 配置正确'));

    // 测试提示词生成
    const failedFile = {
      file: 'test.vue',
      error: 'Cannot find module "element-ui"'
    };
    const prompt = aiRepairer.generateRepairPrompt('test content', failedFile);
    assert(prompt.includes('Vue 2 + Element UI'));
    console.log(chalk.green('  ✅ generateRepairPrompt() 方法正常'));

    passed++;
  } catch (error) {
    console.log(chalk.red('  ❌ AIRepairer 测试失败:'), error.message);
    failed++;
  }

  // 测试 4: 集成测试
  try {
    console.log(chalk.yellow('\n📋 测试集成功能'));

    const tempDir = path.join(__dirname, 'temp-integration');
    await fs.ensureDir(tempDir);

    const aiRepairer = new AIRepairer();
    const buildFixer = new BuildFixer(tempDir, {
      buildCommand: 'echo "test"',
      aiRepairer: aiRepairer
    });

    // 测试集成
    assert.strictEqual(buildFixer.options.aiRepairer, aiRepairer);
    console.log(chalk.green('  ✅ BuildFixer 与 AIRepairer 集成正常'));

    // 测试继承关系
    assert(buildFixer instanceof AIService);
    console.log(chalk.green('  ✅ BuildFixer 继承 AIService 正常'));

    // 清理
    await fs.remove(tempDir);

    passed++;
  } catch (error) {
    console.log(chalk.red('  ❌ 集成测试失败:'), error.message);
    failed++;
  }

  // 输出结果
  console.log(chalk.bold('\n📊 测试结果汇总:'));
  console.log(`总计: ${passed + failed} 个测试`);
  console.log(chalk.green(`✅ 通过: ${passed} 个`));
  console.log(chalk.red(`❌ 失败: ${failed} 个`));

  const successRate = passed + failed > 0
    ? ((passed / (passed + failed)) * 100).toFixed(1)
    : 0;

  console.log(chalk.bold(`🎯 成功率: ${successRate}%`));

  if (failed === 0) {
    console.log(chalk.green('\n🎉 所有测试通过！'));
    return true;
  } else {
    console.log(chalk.red('\n💔 有测试失败'));
    return false;
  }
}

// 运行测试
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error(chalk.red('❌ 测试执行失败:'), error.message);
    process.exit(1);
  });
}

module.exports = { runTests };
